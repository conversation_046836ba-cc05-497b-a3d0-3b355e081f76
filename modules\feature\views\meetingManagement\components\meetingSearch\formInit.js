import { CONSTANTS } from '@/constants.js';
import CommonItems from 'snbcCommon/common/form-items.js';

const { select, input, dateRange, peopleSelector } = CommonItems;

// 会议类型
const meetingType = {
    ...select,
    name: '会议类型',
    modelKey: 'meetingType',
    elOptions: CONSTANTS.MEETING_TYPE.map((item) => ({
        label: item,
        value: item
    }))
};

// 会议时间
const meetingTime = {
    ...dateRange,
    name: '会议时间段',
    modelKey: 'dateRange',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 会议组织形式
const organizeForm = {
    ...select,
    name: '会议组织形式',
    modelKey: 'organizeForm',
    elOptions: ['线上', '线下'].map((item) => ({
        label: item,
        value: item
    }))
};

// 组织人
const organizer = {
    ...peopleSelector,
    name: '组织人',
    modelKey: 'organizer',
    elSelectAttrs: {
        isMultipled: false
    }
};

// 会议状态
const meetingStatus = {
    ...select,
    name: '会议状态',
    modelKey: 'meetingStatus',
    elSelectAttrs: {},
    elOptions: CONSTANTS.MEETING_STATUS.map((item) => ({
        label: item,
        value: item
    }))
};

// 会议纪要状态
const meetingMinutesStatus = {
    ...select,
    name: '纪要任务完成状态',
    modelKey: 'meetingMinutesStatus',
    elSelectAttrs: {},
    elOptions: CONSTANTS.MEETING_MINUTES_STATUS.map((item) => ({
        label: item,
        value: item
    }))
};

// 关联项目
const projectId = {
    ...select,
    name: '关联项目',
    modelKey: 'projectId',
    elSelectAttrs: {
        'clearable': true,
        'remote': true,
        'filterable': true,
        'remote-method': '',
        'style': 'width:100%'
    },
    elOptions: []
};

// 会议名称
const meetingTitle = {
    ...input,
    name: '会议名称关键字',
    modelKey: 'meetingTitle',
    elInputAttrs: {}
};

// 查询参数初始化
export const queryParams = {
    meetingType: '',
    meetingStatus: '',
    organizeForm: '',
    organizer: '',
    meetingMinutesStatus: '',
    dateRange: [],
    projectId: '',
    meetingTitle: '',
    meetingMinutesFlag: false,
    type: 2
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '125px'
    },
    items: [
        meetingType,
        meetingTime,
        organizeForm,
        organizer,
        meetingStatus,
        meetingMinutesStatus,
        projectId,
        meetingTitle
    ]
};

// 导航栏配置项
export const navItems = [
    { field: 2, name: '我参加的', queryField: 'type' },
    { field: 1, name: '我组织的', queryField: 'type' },
    { field: 3, name: '我提出过要求的', queryField: 'type' },
    { field: 4, name: '会议任务责任人是我的', queryField: 'type' },
    { field: 5, name: '抄送给我的', queryField: 'type' }
];
