<!-- 会议任务追踪 -->
<template>
    <div class="meeting-track-container">
        <CollapsibleSearchPanel
            :queryParams="queryParams"
            :queryConfig="queryConfig"
            :isDot="isDot"
            :navItems="navItems"
            v-model="navActiveName"
            @navChange="handleNavChange"
            @search="handleSearch"
            @reset="handleReset"
        >
        </CollapsibleSearchPanel>
        <el-table
            class="meeting-trace-table"
            :data="tableList"
            :header-cell-style="{
                'text-align': 'center',
                'border': '1px solid #8c8c8c'
            }"
            :cell-style="{
                'vertical-align': 'top',
                'border': '1px solid #8c8c8c!important'
            }"
            height="calc(100vh - 172px)"
            empty-text="无会议任务数据"
            :span-method="objectSpanMethod"
        >
            <el-table-column
                prop="meetingTitle"
                label="会议名称"
                width="200"
                align="left"
            >
            </el-table-column>
            <el-table-column
                prop="problemItem"
                label="问题/事项"
                width="280"
                align="left"
            >
            </el-table-column>
            <el-table-column
                align="center"
                prop="creatorName"
                label="提出人"
                width="80"
            >
            </el-table-column>
            <el-table-column prop="meetingRequire" label="会议要求" width="280">
            </el-table-column>
            <el-table-column
                prop="responsibleName"
                label="责任人"
                align="center"
                width="80"
            >
            </el-table-column>
            <el-table-column
                prop="planFinishDate"
                label="计划完成时间"
                align="center"
                width="100"
            >
            </el-table-column>
            <el-table-column
                prop="finishStatus"
                label="任务状态"
                align="center"
                width="100"
            >
            </el-table-column>
            <el-table-column prop="finishDesc" label="完成情况">
            </el-table-column>
            <el-table-column
                label="操作"
                align="center"
                width="90"
                fixed="right"
            >
                <template slot-scope="scope">
                    <el-button type="primary" @click="handleEdit(scope.row)"
                        >编辑</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <pagination
            class="pagination"
            :total="total"
            :page.sync="page"
            :limit.sync="limit"
            @pagination="getList"
        />
        <MeetingTraceTaskDialog
            :visible.sync="dialogVisible"
            :data="traceTaskData"
            @update="getList"
        ></MeetingTraceTaskDialog>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants.js';
import Pagination from 'wtf-core-vue/src/components/Pagination';
import CollapsibleSearchPanel from 'Components/CollapsibleSearchPanel.vue';
import { getUserAccount } from '../../commonFunction';
import MeetingTraceTaskDialog from './meetingTraceTaskDialog.vue';
import { queryParams, queryConfig, navItems } from './formInit.js';

export default {
    name: 'MeetingTaskTrace',
    components: { Pagination, MeetingTraceTaskDialog, CollapsibleSearchPanel },
    data() {
        return {
            dialogVisible: false,
            // 导航栏激活项
            navActiveName: '我参加的',
            // 搜索面板红点状态
            isDot: false,
            // 查询参数
            queryParams: { ...queryParams },
            // 导航栏配置
            navItems,
            // 所有关联项目
            relatedProjectsOptions: [],
            searchOptions: [],
            // 会议跟踪任务数据
            traceTaskData: {},
            CONSTANTS,
            tableList: [],
            total: 0,
            page: 1,
            limit: 50
        };
    },
    computed: {
        // 动态查询配置（包含关联项目）
        queryConfig() {
            const config = { ...queryConfig };
            const relateProjectConfig = config.items.find(
                (i) => i.modelKey === 'projectId'
            );
            relateProjectConfig.elSelectAttrs['remote-method'] =
                this.remoteMethod;
            relateProjectConfig.elOptions = this.searchOptions.map((item) => ({
                label: item.projectName,
                value: item.projectId
            }));
            return config;
        }
    },
    created() {
        this.getRelatedProjectsOptions();
        this.getList();
    },
    activated() {
        this.getRelatedProjectsOptions();
        this.getList();
    },
    methods: {
        // 导航栏切换事件
        handleNavChange(item) {
            // 根据导航项设置查询参数
            if (item.queryField && item.field !== '') {
                this.queryParams[item.queryField] = item.field;
            } else {
                this.queryParams.type = '';
            }
            this.page = 1;
            this.getList();
        },
        // 搜索事件
        handleSearch() {
            this.navActiveName = '';
            this.queryParams.type = '';
            this.page = 1;
            this.getList();
        },
        // 重置事件
        handleReset() {
            this.queryParams = { ...queryParams };
            this.navActiveName = '会议任务责任人是我的';
            this.page = 1;
            this.limit = 50;
            this.getList();
        },
        async getList() {
            const api = this.$service.feature.meetingTaskTrace.getList;
            const params = {
                ...this.queryParams,
                currentPage: this.page,
                startDate: this.queryParams.daterange
                    ? this.queryParams.daterange[0]
                    : '',
                endDate: this.queryParams.daterange
                    ? this.queryParams.daterange[1]
                    : '',
                myAccount: getUserAccount(this),
                pageSize: this.limit,
                meetingRequire: this.queryParams.meetingRequire,
                type: this.queryParams.type || '',
                projectId: this.queryParams.projectId
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.tableList = res.body.list;
                    this.total = res.body.total;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },

        /**
         * 获取所有项目用于下拉框选项
         */
        async getRelatedProjectsOptions() {
            try {
                const api =
                    this.$service.department.naturalResources.getProjectselect;
                const res = await api();
                if (res.head.code === '000000') {
                    this.relatedProjectsOptions = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (err) {
                console.error(err);
            }
        },
        handleEdit(data) {
            this.dialogVisible = true;
            this.traceTaskData = data;
        },
        /**
         * 远程搜索，解决过量数据卡顿问题
         * @param {Object} query 参数
         */
        remoteMethod(query) {
            if (query !== '') {
                this.searchOptions = this.relatedProjectsOptions.filter(
                    (item) => {
                        return item.projectName.indexOf(query) > -1;
                    }
                );
            } else {
                this.searchOptions = [];
            }
        },
        /**
         * 合并单元格
         * @param {Object} param 表格数据
         * @returns {Function} 合并单元格
         */
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            // 只处理会议名称、事项/问题、提出人、会议要求的合并
            if (![0, 1, 2, 3].includes(columnIndex)) {
                return {
                    rowspan: 1,
                    colspan: 1
                };
            }

            // 根据列确定要比较的字段
            const getCompareField = (colIndex) => {
                switch (colIndex) {
                    case 0:
                        return 'meetingTitle';
                    case 1:
                        return 'problemItem';
                    case 2:
                        return 'creatorName';
                    case 3:
                        return 'meetingRequire';
                    default:
                        return '';
                }
            };

            const compareField = getCompareField(columnIndex);
            if (rowIndex === 0) {
                let count = 1;
                for (let i = 1; i < this.tableList.length; i++) {
                    if (
                        this.tableList[i][compareField] === row[compareField] &&
                        this.tableList[i].meetingId === row.meetingId
                    ) {
                        count += 1;
                    } else {
                        break;
                    }
                }
                return {
                    rowspan: count,
                    colspan: 1
                };
            }

            // 与上一行比较，判断是否需要合并
            const prevRow = this.tableList[rowIndex - 1];
            // 必须是相同会议才会进行合并
            if (
                prevRow[compareField] === row[compareField] &&
                prevRow.meetingId === row.meetingId
            ) {
                return {
                    rowspan: 0,
                    colspan: 0
                };
            }

            // 计算当前行需要合并的行数
            let count = 1;
            for (let i = rowIndex + 1; i < this.tableList.length; i++) {
                if (
                    this.tableList[i][compareField] === row[compareField] &&
                    this.tableList[i].meetingId === row.meetingId
                ) {
                    count += 1;
                } else {
                    break;
                }
            }

            return {
                rowspan: count,
                colspan: 1
            };
        }
    }
};
</script>

<style lang="scss" scoped>
.meeting-trace-table {
    margin-top: 10px;
    border: 1px solid #8c8c8c !important;
}

.pagination {
    padding: 5px 0;
    margin: 10px 0 0 0;
}

.meeting-trace-table .meeting-title-button {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    padding: 0px !important;
    max-width: 100%;
    height: 23px;
}

// 统一表头高度，修正固定列错位
::v-deep .el-table__header {
    padding: 0;
    height: 50px !important;
}

// 调整table表头颜色
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
</style>
