<!-- 用于创建/编辑会议 -->
<template>
    <div>
        <el-dialog
            title="线上评审意见"
            :visible.sync="dialogVisible"
            width="85%"
        >
            <HeaderInfo
                :meetingInfo="headerInfo.meetingInfo"
                :meetingPartRelateList="headerInfo.meetingPartRelateList"
            ></HeaderInfo>
            <el-form
                ref="onlineReviewForm"
                :model="onlineReviewForm"
                class="minutes-form"
            >
                <el-table
                    :data="onlineReviewForm.onlineReviewData"
                    class="minutes-table"
                    :span-method="objectSpanMethod"
                    :row-style="{ background: '#fff' }"
                    :row-class-name="rowClassName"
                >
                    <el-table-column
                        prop="prop"
                        label="线上评审意见"
                        align="center"
                        empty-text="无线上评审意见"
                    >
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="reviewItem"
                            label="项"
                            width="150"
                        >
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="reviewConclusion"
                            width="220"
                        >
                            <template #header>
                                <RedStar class="required" />结论
                            </template>
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`onlineReviewData.${scoped.$index}.reviewConclusion`"
                                >
                                    <div
                                        class="flex"
                                        style="
                                            justify-content: center;
                                            align-items: center;
                                        "
                                    >
                                        <el-radio-group
                                            v-model="
                                                scoped.row.reviewConclusion
                                            "
                                            @input="
                                                conclusionChange(
                                                    scoped.row.reviewItem,
                                                    scoped.row
                                                )
                                            "
                                            :disabled="
                                                (scoped.row.judgesAccount !==
                                                    user &&
                                                    !combinedPermission) ||
                                                scoped.row.reviewItem ===
                                                    '多决策评委时最终决策'
                                            "
                                        >
                                            <el-radio label="通过"></el-radio>
                                            <el-radio label="不通过"></el-radio>
                                        </el-radio-group>
                                    </div>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="additionalView"
                        >
                            <template #header>
                                <RedStar class="required" />附加意见
                            </template>
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`onlineReviewData.${scoped.$index}.additionalView`"
                                >
                                    <el-input
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        maxlength="500"
                                        v-model="scoped.row.additionalView"
                                        :disabled="
                                            (scoped.row.judgesAccount !==
                                                user &&
                                                !combinedPermission) ||
                                            scoped.row.reviewItem ===
                                                '多决策评委时最终决策'
                                        "
                                    ></el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="judgesName"
                            label="评委签字"
                        >
                            <template #default="scoped">
                                <div class="flex">
                                    <div style="width: 50%; text-align: right">
                                        {{ scoped.row.judgesRole }}
                                    </div>
                                    <div style="width: 50%">
                                        {{
                                            scoped.row.reviewConclusion
                                                ? scoped.row.judgesName
                                                : ''
                                        }}
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table-column>
                </el-table>
            </el-form>
            <div slot="footer">
                <el-button @click="closeDialog">取 消</el-button>
                <el-button type="primary" @click="save">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import HeaderInfo from './HeaderInfo.vue';
import {
    getUserAccount,
    isOrganizerOrWriter,
    isObjectDifferent
} from '../../commonFunction';
import RedStar from 'feature/components/redStar';
import moment from 'moment';

export default {
    name: 'OnlineReviewOpinion',
    components: { HeaderInfo, RedStar },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        meetingId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            headerInfo: {
                meetingInfo: { meetingTitle: '' }
            },
            // 会议结论
            meetingConclusion: '',
            onlineReviewForm: {
                onlineReviewData: []
            },
            // 线上评审意见原始数据
            oriData: [],
            // 当前用户的域账号
            user: '',
            // 会议数据管理员专有的编辑权限
            dataManagerPermission:
                this.$store.state.permission.btnDatas.includes(
                    'MeetingDataManagerEditButton'
                )
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        // 是否只有一个决策评委
        isSingleReviewer() {
            const reviewerList = this.onlineReviewForm.onlineReviewData.filter(
                (i) => i.reviewItem === '决策评委'
            );
            return reviewerList.length === 1;
        },
        // 会议纪要是否审核通过
        isMinutesPass() {
            return (
                this.headerInfo.meetingInfo.minutesStatus === '任务未关闭' ||
                this.headerInfo.meetingInfo.minutesStatus === '任务已关闭'
            );
        },
        // 组织者或编写人是否有编辑权限（在恰当的时机）
        isOrganizerOrWriterHasPermission() {
            if (
                this.headerInfo.meetingInfo?.meetingStatus === '已取消' ||
                (this.headerInfo.meetingInfo?.meetingStatus === '结束' &&
                    this.headerInfo.meetingInfo?.minutesStatus === '无纪要')
            ) {
                return false;
            }
            const bool = isOrganizerOrWriter(
                this.headerInfo.meetingPartRelateList
            );
            return bool;
        },
        combinedPermission() {
            return (
                (this.isOrganizerOrWriterHasPermission &&
                    !this.isMinutesPass) ||
                this.dataManagerPermission
            );
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.user = getUserAccount(this);

                this.getMeetingInfo();
                this.getOnlineReviewData();
            }
        }
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
        },
        /**
         * 评审组织形式修改时的变更
         * @param {String} value 当前值
         */
        handleIsOnlineChange(value) {
            if (value === '线上') {
                this.form.meetingLocation = '项目管理信息化平台';
            } else {
                this.form.meetingLocation = '';
            }
        },
        /**
         * 删除项
         * @param {Number} index 序号
         * @param {String} type 评委列表或者参会人员列表
         */
        decrease(index, type) {
            this.form[type].splice(index, 1);
        },
        /**
         * 处理数据
         * @return {Array} 处理后的数据
         */
        handleData() {
            let data;
            let oriData;
            if (this.combinedPermission) {
                data = this.onlineReviewForm.onlineReviewData.filter(
                    (i) => i.reviewItem !== '多决策评委时最终决策'
                );
                oriData = this.oriData.filter(
                    (i) => i.reviewItem !== '多决策评委时最终决策'
                );
            } else {
                data = this.onlineReviewForm.onlineReviewData
                    .filter((i) => i.judgesAccount === this.user)
                    .filter((i) => i.reviewItem !== '多决策评委时最终决策');
                oriData = this.oriData
                    .filter((i) => i.judgesAccount === this.user)
                    .filter((i) => i.reviewItem !== '多决策评委时最终决策');
            }
            // 判断数据是否有更新，如果有变更就更新评审时间
            data.forEach((changedItem) => {
                // 如果不存在评审时间，就默认当前时间
                if (!changedItem.reviewDate) {
                    changedItem.reviewDate = moment().format('YYYY-MM-DD');
                    return;
                }
                // 存在评审时间，就将变更后的与原始数据进行比较，如果有变更就更新评审时间
                const oriItem = oriData.find((i) => i.id === changedItem.id);

                const isDifferent = isObjectDifferent(oriItem, changedItem);

                if (isDifferent) {
                    changedItem.reviewDate = moment().format('YYYY-MM-DD');
                }
            });

            return data;
        },
        /**
         * 保存
         */
        async save() {
            const data = this.handleData();
            let valid = true;
            data.forEach((i) => {
                if (!i.additionalView.trim() || !i.reviewConclusion) {
                    valid = false;
                }
            });
            if (!valid) {
                this.$message.warning('请填写结论与附加意见');
                return;
            }
            // 这里最后一个请求等前面所有请求结束后发送，避免并发导致的问题
            const last = data.pop();
            const api = this.$service.feature.onlineReview.edit;
            try {
                await Promise.all(
                    data.map(async (item) => {
                        const res = await api({
                            ...item,
                            meetingId: this.meetingId,
                            meetingType: this.headerInfo.meetingInfo.meetingType
                        });
                        if (res.head.code !== '000000') {
                            throw new Error(res.head.message);
                        }
                    })
                );
                const lastRes = await api({
                    ...last,
                    meetingId: this.meetingId,
                    meetingType: this.headerInfo.meetingInfo.meetingType
                });
                if (lastRes.head.code !== '000000') {
                    throw new Error(lastRes.head.message);
                }
                this.$message.success('保存成功');
                this.closeDialog();
            } catch (error) {
                console.error(error);
                this.$message.error(error.message || '系统异常');
            }
        },
        /**
         * 表格行的操作方法
         * @returns {Object} 行列规则
         */
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            // 只在“项”这一列进行合并, 其他保持现状
            if (columnIndex !== 0) {
                return {
                    rowspan: 1,
                    colspan: 1
                };
            }
            // 如果当前行的“项”与上一行的“项”相同，则合并
            if (
                rowIndex > 0 &&
                row.reviewItem ===
                    this.onlineReviewForm.onlineReviewData[rowIndex - 1]
                        .reviewItem
            ) {
                return {
                    // 隐藏当前行的单元格
                    rowspan: 0,
                    colspan: 0
                };
            }
            // 计算当前“项”需要合并的行数
            let rowspan = 1;
            for (
                let i = rowIndex + 1;
                i < this.onlineReviewForm.onlineReviewData.length;
                i++
            ) {
                if (
                    row.reviewItem ===
                    this.onlineReviewForm.onlineReviewData[i].reviewItem
                ) {
                    rowspan += 1;
                } else {
                    break;
                }
            }
            return {
                rowspan,
                colspan: 1
            };
        },
        /**
         * 获取会议信息
         */
        async getMeetingInfo() {
            const api = this.$service.feature.meeting.getMeetingInfo;
            try {
                const res = await api({
                    meetingId: this.meetingId
                });
                if (res.head.code === '000000') {
                    this.headerInfo = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 获取线上评审意见
         */
        async getOnlineReviewData() {
            const api = this.$service.feature.onlineReview.getInfo;
            try {
                const res = await api({
                    meetingId: this.meetingId
                });
                if (res.head.code === '000000') {
                    this.onlineReviewForm.onlineReviewData =
                        this.$tools.cloneDeep(res.body);
                    this.oriData = this.$tools.cloneDeep(res.body);
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 每一行的回调
         * @param {Object} param0 行
         * @return {String} 类名
         */
        rowClassName({ row }) {
            // 如果只有一个评委，隐藏‘多评委决策’行
            if (
                row.reviewItem === '多决策评委时最终决策' &&
                this.isSingleReviewer
            ) {
                return 'hide-row';
            }
        },
        /**
         * 结论变更时的回调
         * @param {String} item 项
         * @param {Object} row 每行的数据
         */
        conclusionChange(item, row) {
            if (row.reviewConclusion === '通过' && row.additionalView === '') {
                row.additionalView = '无附加意见';
            } else if (row.reviewConclusion === '不通过') {
                row.additionalView = '';
            }
            if (item !== '决策评委') return;
            const list = this.onlineReviewForm.onlineReviewData;
            // 决策评委最终有一个不通过，结论就是不通过
            const conclusionList = list
                .filter((i) => i.reviewItem === '决策评委')
                .map((i) => i.reviewConclusion);
            let conclusion = '通过';
            conclusionList.forEach((i) => {
                if (!i) {
                    conclusion = '';
                    return;
                }
                if (i === '不通过') {
                    conclusion = '不通过';
                }
            });
            list[list.length - 1].reviewConclusion = conclusion;
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.wrap {
    flex-wrap: wrap;
}
.meeting-material {
    margin-bottom: 12px;
}

.meeting-conclusion {
    background-color: white;
    width: 100%;
    color: black;
    padding: 15px 0;
}

.add-minutes {
    float: right;
    margin-top: 15px;
    margin-bottom: 5px;
}

.minutes-form {
    margin-top: 15px;
    ::v-deep .hide-row {
        display: none;
    }
    ::v-deep .el-table__row > td {
        padding: 5px;
    }
    ::v-deep .cell {
        padding: 0 !important;
    }
    .el-form-item {
        margin-bottom: 0px;
    }
    ::v-deep .el-form-item__content {
        margin: 0 !important;
    }
    // 令输入框无边框
    ::v-deep .el-textarea__inner {
        border: none;
        resize: none;
    }
    // 令鼠标移入之后不变色
    ::v-deep .el-table tbody tr:hover > td {
        background-color: #fff !important;
    }
    .minutes-table {
        border: 1px solid #8c8c8c !important;
    }
}

.date-picker {
    width: 100%;
}
.required {
    margin-right: 2px;
}
::v-deep .meeting-conclusion .el-form-item__label {
    font-weight: bold;
}
::v-deep.form .el-form-item__label {
    font-weight: bold;
    padding-right: 10px;
    width: fit-content;
}
.review-form {
    margin-left: 40px;
}
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
}
</style>
