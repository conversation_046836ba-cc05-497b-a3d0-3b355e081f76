import { CONSTANTS } from '@/constants.js';
import CommonItems from 'snbcCommon/common/form-items.js';

const { select, input, dateRange, peopleSelector } = CommonItems;

// 会议类型
const meetingType = {
    ...select,
    name: '会议类型',
    modelKey: 'meetingType',
    elOptions: CONSTANTS.MEETING_TYPE.map((item) => ({
        label: item,
        value: item
    }))
};

// 组织人
const organizer = {
    ...peopleSelector,
    name: '组织人',
    modelKey: 'organizer',
    elSelectAttrs: {
        isMultipled: false
    }
};

// 要求提出人
const creator = {
    ...peopleSelector,
    name: '要求提出人',
    modelKey: 'creator',
    elSelectAttrs: {
        isMultipled: false
    }
};

// 任务责任人
const responsiblePerson = {
    ...peopleSelector,
    name: '任务责任人',
    modelKey: 'responsiblePerson',
    elSelectAttrs: {
        isMultipled: false
    }
};

// 关联项目
const projectId = {
    ...select,
    name: '关联项目',
    modelKey: 'projectId',
    elSelectAttrs: {
        'clearable': true,
        'remote': true,
        'filterable': true,
        'remote-method': '',
        'style': 'width:100%'
    },
    elOptions: []
};

// 会议要求关键字
const meetingRequire = {
    ...input,
    name: '会议要求关键字',
    modelKey: 'meetingRequire'
};

// 查询参数初始化
export const queryParams = {
    meetingType: '',
    organizer: '',
    creator: '',
    responsiblePerson: '',
    projectId: '',
    daterange: [],
    meetingRequire: '',
    type: 2
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '140px'
    },
    items: [
        meetingType,
        organizer,
        creator,
        responsiblePerson,
        projectId,
        meetingRequire
    ]
};

// 导航栏配置项
export const navItems = [
    { field: '', name: '所有', queryField: 'type' },
    { field: 1, name: '我组织的', queryField: 'type' },
    { field: 2, name: '我参加的', queryField: 'type' },
    { field: 3, name: '我提出的', queryField: 'type' },
    { field: 4, name: '责任人是我的', queryField: 'type' }
];
