/* eslint-disable line-comment-position */
/* eslint-disable no-inline-comments */
// 常量
export const CONSTANTS = {
    // 技术中心对应的部门代号
    TECH_CENTER_CODE: '0002',
    // 各个产品线的名称以及对应的禅道项目code
    PRODUCT_LINE: [
        { label: '打印扫描', code: '2' },
        { label: '识别', code: '3' },
        { label: '自助终端', code: 'AUTOEND' },
        { label: '智能柜', code: '5' },
        { label: '自助售货', code: '6' },
        { label: '自动化', code: '7' },
        { label: '关键基础零部件', code: '8611' },
        { label: '系统集成', code: 'SYSTEM' },
        { label: '技术研发', code: 'TECH' },
        { label: '对外合作', code: 'OUTSIDECORP' }
    ],
    // 立项类型
    APPROVAL_TYPE: ['开发立项', '预研立项'],
    // 需求类型
    DEMAND_CATEGORY: ['通用产品', '定制产品'],
    // 产品类型
    PRODUCT_TYPE: [
        '全新产品',
        '产品升级',
        'OEM产品',
        '成熟技术衍生产品分布',
        '其他'
    ],
    // 项目状态
    PROJECT_STATUS: [
        { label: '市场待立项', value: '市场待立项' },
        { label: '排队中', value: '排队中' },
        { label: '进行中', value: '进行中' },
        { label: '已暂停', value: '已暂停' },
        { label: '已终止', value: '已终止' },
        { label: '已结项', value: '已结项' }
    ],
    // 项目归类
    PROJECT_CLASSFICATION: [
        '市场待立项项目',
        '有需求决议函，技术中心排队项目',
        '有需求决议函，技术中心进行中项目'
    ],
    // 一级原因
    LEVEL_RRASON: [
        {
            label: '市场原因',
            value: '市场原因',
            subReasons: [
                { label: '客户需求变更', value: '客户需求变更' },
                { label: '客户需求不明确', value: '客户需求不明确' }
            ]
        },
        {
            label: '技术原因',
            value: '技术原因',
            subReasons: [
                { label: '设计方案反复', value: '设计方案反复' },
                { label: '一般问题解决延期', value: '一般问题解决延期' },
                {
                    label: '疑难问题--已上升至技术委员会',
                    value: '疑难问题--已上升至技术委员会'
                },
                {
                    label: '疑难问题--未上升至技术委员会',
                    value: '疑难问题--未上升至技术委员会'
                }
            ]
        },
        {
            label: '资源原因',
            value: '资源原因',
            subReasons: [
                { label: '人力资源冲突', value: '人力资源冲突' },
                { label: '员工能力欠缺', value: '员工能力欠缺' }
            ]
        },
        {
            label: '管控原因',
            value: '管控原因',
            subReasons: [
                { label: '计划制定不合理', value: '计划制定不合理' },
                { label: '工作推进不及时', value: '工作推进不及时' },
                { label: '风险管控缺失', value: '风险管控缺失' },
                { label: '沟通配合不到位', value: '沟通配合不到位' }
            ]
        },
        {
            label: '物料原因',
            value: '物料原因',
            subReasons: [
                { label: '物料交期延误', value: '物料交期延误' },
                { label: '物料质量不合格', value: '物料质量不合格' }
            ]
        },
        {
            label: '成本原因',
            value: '成本原因',
            subReasons: [
                { label: '成本目标变更/不合理', value: '成本目标变更/不合理' },
                { label: '成本目标合理但未达成', value: '成本目标合理但未达成' }
            ]
        }
    ],
    // 关联模块
    KEY_MODULES: [
        {
            label: '结构',
            value: '结构'
        },
        {
            label: '电气',
            value: '电气'
        },
        {
            label: '固件',
            value: '固件'
        },
        {
            label: '软件',
            value: '软件'
        },
        {
            label: '测试',
            value: '测试'
        },
        {
            label: '工艺',
            value: '工艺'
        },
        {
            label: '算法',
            value: '算法'
        }
    ],
    // 产品类别
    PRODUCT_CATEGORY: ['硬件类', '软件类'],
    // 市场定位
    MARKET_POSITIONING: ['国内', '海外', '全球'],
    // 市场重要级别
    PRIORITY_RATING: ['战略+迭代', '大客户', '维护', '其他'],
    // 销售类型
    SALES_TYPE: ['支撑当年销售', '不支撑当年销售'],
    // 业务板块
    BUSINESS_SEGMENT: [
        '金融行业',
        '物流行业',
        '智能自助终端',
        '专用打印扫描',
        '服务运营',
        '关键基础零部件'
    ],
    // 产品线内部排序
    INTERNAL_PRODUCT_RANKING: ['I类', 'II类', 'III类'],
    // 项目控制等级
    PROJECT_CONTROL_LEVEL: ['I级', 'II级', 'III级'],
    // 项目技术难度
    TECHNICAL_COMPLEXITY: ['A级', 'B级', 'B+级', 'C级'],
    // 开发流程
    DEVELOPMENT_PROCESS: ['MPD', 'ITPD', 'AEPD', 'TPD', '委外项目'],
    // 归属
    OWNERSHIP: ['新北洋', '数码', '荣鑫', '正棋'],
    // 需求导入主体
    DEMAND_IMPORT_SUBJECT: ['市场规划导入', '研发自主规划'],
    // 级别
    LEAVEL_GRADE: [
        {
            label: '助理',
            value: '助理'
        },
        {
            label: '初级',
            value: '初级'
        },
        {
            label: '中级',
            value: '中级'
        },
        {
            label: '高级',
            value: '高级'
        },
        {
            label: '资深',
            value: '资深'
        },
        {
            label: '专家',
            value: '专家'
        }
    ],
    // 岗位
    POST_STATION: [
        {
            label: '结构',
            value: '结构'
        },
        {
            label: '硬件',
            value: '硬件'
        },
        {
            label: '固件',
            value: '固件'
        },
        {
            label: '测试',
            value: '测试'
        },
        {
            label: '软件',
            value: '软件'
        },
        {
            label: '算法',
            value: '算法'
        },
        {
            label: '软件测试',
            value: '软件测试'
        },
        {
            label: '结构工艺',
            value: '结构工艺'
        },
        {
            label: '电气工艺',
            value: '电气工艺'
        },
        {
            label: '项目管理',
            value: '项目管理'
        }
    ],
    // 项目角色
    PROJECT_ROLE: [
        {
            label: '项目经理',
            value: '项目经理'
        },
        {
            label: '开发代表',
            value: '开发代表'
        },
        {
            label: '开发工程师',
            value: '开发工程师'
        }
    ],
    // 任务书类型
    TASK_BOOK_TYPE: [
        '产品预研任务书',
        '产品开发任务书',
        '技术预研任务书',
        '技术开发任务书',
        '委外开发项目任务书'
    ],
    // 当前项目状态
    CURRENT_SCHEDULE_STATUS: ['正常进行', '延期进行'],
    // 所属阶段
    PROJECT_STAGE: ['概念阶段', '计划阶段', '开发阶段', '验证阶段', '迁移阶段'],
    // 用于echarts等图表绘制的color集合
    COLOR_LIST: [
        '#2C7FB8', // 深蓝色
        '#FF9A3C', // 橙色
        '#4CAF50', // 绿色
        '#D32F2F', // 红色
        '#AB47BC', // 紫色
        '#90CAF9', // 浅蓝色
        '#CDDC39', // 黄绿色
        '#F06292', // 粉色
        '#9E9E9E', // 灰色
        '#26C6DA', // 青色
        '#FFB74D', // 浅橙色
        '#AED581', // 浅绿色
        '#795548', // 棕色
        '#FF7043', // 浅红色
        '#B39DDB', // 浅紫色
        '#BCAAA4' // 浅棕色
    ],
    // 会议类型
    MEETING_TYPE: [
        '一级TR评审',
        '二级TR评审',
        'DCP评审',
        '技术/产品讨论会议',
        '管理相关会议',
        '部门例会',
        '项目例会',
        '其他会议'
    ],
    // 参会情况
    MEETING_ATTEND: [
        '参会',
        '无异议不参会',
        '缺席',
        '请假',
        '指派他人参会决策'
    ],
    // 会议纪要状态
    MEETING_MINUTES_STATUS: [
        '撰写中',
        '审核中',
        '任务未关闭',
        '任务已关闭',
        '无纪要'
    ],
    // 会议状态
    MEETING_STATUS: ['待召开', '已结束'],
    // 任务状态
    TASK_STATUS: ['未完成', '已完成'],
    // 评委选择合理性
    REVIEWER_REASONABLENESS: [
        {
            label: '2分（合理）：需参与决策的评委均参会或已提前给出结论，并合理邀请技术委员会专家参会',
            value: 2
        },
        {
            label: '1分（不合理）：需参与决策的评委未参会或未提前给出结论，或应邀请技术委员会专家参会但未邀请',
            value: 1
        }
    ],
    // 预审质量
    PREVIEW_QUALITY: [
        {
            label: '3分（高）：大多数评委在会前提出预审意见，提升了评审效率',
            value: 3
        },
        {
            label: '2分（一般）：部分评委应该在会前提出预审意见但未提出',
            value: 2
        },
        {
            label: '1分（差）：极少数或没有评委在会前提出预审意见，降低了评审效率',
            value: 1
        },
        {
            label: '本次会议无需评委提出预审意见，且未对评审效率产生影响，无需评价',
            value: 0
        }
    ],
    // 评审质量
    REVIEW_QUALITY: [
        {
            label: '3分（高）：评审突出重点，会上重点围绕关键问题及风险点研讨并形成决策，评审效率高',
            value: 3
        },
        {
            label: '2分（一般）：评审突出重点，会上基本围绕关键问题及风险点研讨并形成决策，但存在个别待改善事项',
            value: 2
        },
        {
            label: '1分（差）：评审未突出重点，会上过多陷入细节研讨或研讨偏离主题，评审效率低',
            value: 1
        }
    ],
    // 战略/非战略
    STRATEGY: ['战略', '非战略'],
    // 金融机具场景
    FINANCE_SCENE: [
        {
            category: '国内网点',
            specific: [
                '管理平台',
                '',
                '柜员场景',
                '大堂场景',
                '服务场景',
                '外拓营销'
            ]
        }
    ],
    // 圈内数字对应的数组
    CIRCLED_NUMBERS: [
        '①',
        '②',
        '③',
        '④',
        '⑤',
        '⑥',
        '⑦',
        '⑧',
        '⑨',
        '⑩',
        '⑪',
        '⑫',
        '⑬',
        '⑭',
        '⑮',
        '⑯',
        '⑰',
        '⑱',
        '⑲',
        '⑳'
    ],
    // 缺陷状态
    DEFECT_STATUS: ['激活', '关闭', '非缺陷'],
    // OA流程状态
    OA_FLOW_STATUS: ['审批', '归档', '取消'],
    // 风险关联对象
    RISK_RELATED_OBJECT: [
        '维护项目任务',
        '维护项目缺陷',
        '维护项目需求',
        '订单'
    ],
    // 风险关联对象(开发项目)
    RISK_RELATED_OBJECT_DEVELOP: ['开发项目任务', '开发项目订单'],
    // 缺陷严重等级
    DEFECT_LEVEL: ['致命的', '严重的', '一般的', '轻微的', '建议的'],
    // 缺陷来源
    DEFECT_SOURCE: [
        '内部测试及集成测试',
        '系统测试',
        '验收测试',
        '中试试制及验证',
        '工艺性审核',
        '定制样品',
        '售后服务（TOP问题）',
        '电子流（顾客投诉OA）',
        '市场验证',
        '会议',
        '出差报告'
    ],
    // 风险类型
    RISK_TYPE: [
        '计划管理',
        '需求管理',
        '成本管理',
        '评审管理',
        '问题管理',
        '技术风险',
        '采购管理',
        '样机管理',
        '模具管理',
        '沟通管理',
        '人力管理',
        '其他'
    ],
    // 风险状态
    RISK_STATUS: ['进行中', '已关闭', '转为非风险', '转为问题'],
    // 风险等级
    RISK_LEVEL: ['高', '中', '低'],
    // 风险应对需支持事项状态
    RISK_SUPPORT_STATUS: ['激活', '关闭'],
    // 优先级别
    PRIORITY_LEVEL: ['高', '低']
};
