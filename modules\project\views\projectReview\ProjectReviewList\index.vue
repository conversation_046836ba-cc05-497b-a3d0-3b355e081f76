<template>
    <div>
        <el-table
            class="review-list"
            :data="reviewList"
            :header-cell-style="{
                'text-align': 'center'
            }"
            height="calc(100vh - 150px)"
        >
            <el-table-column
                label="评审名称"
                width=""
                :show-overflow-tooltip="true"
            >
                <template slot-scope="scope">
                    <div>
                        <el-button
                            class="meeting-title"
                            type="text"
                            @click="handleMeetingClick(scope.row)"
                            :style="{
                                'max-width': isMinutesFinished(scope.row)
                                    ? 'calc(100% - 15px)'
                                    : '100%'
                            }"
                        >
                            {{ scope.row.meetingTitle }}
                        </el-button>
                        <i
                            v-if="isMinutesFinished(scope.row)"
                            class="el-icon-document"
                            style="color: #3370ff"
                        ></i>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                prop="meetingType"
                label="评审类型"
                width="120"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="meetingTime"
                label="评审时间"
                width="140"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="meetingConclusion"
                label="评审结论"
                width="100"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="meetingView"
                label="评审意见"
            ></el-table-column>
            <el-table-column label="评审事项跟踪" width="150" align="center">
                <template slot-scope="scope">
                    {{
                        scope.row.minuteTaskNum > 0
                            ? `${scope.row.minuteTaskNum} 条事项待完成`
                            : `无跟踪事项`
                    }}
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'ProjectReviewList',
    components: {},
    props: {
        activeName: {
            type: String,
            default: ''
        },
        projectId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            reviewList: []
        };
    },
    computed: {},
    watch: {
        activeName(newVal) {
            if (newVal === 'reviewList') {
                this.getReviewList();
            }
        },
        projectId() {
            if (this.activeName === 'reviewList') {
                this.getReviewList();
            }
        }
    },
    mounted() {
        this.getReviewList();
    },
    activated() {
        this.getReviewList();
    },
    methods: {
        /**
         * 获取列表数据
         */
        async getReviewList() {
            if (!this.projectId) return;
            const api = this.$service.project.review.getMeetingList;
            const params = { projectId: this.projectId };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.reviewList = res.body;
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 点击会议跳转到详情页
         * @param {Objcet} row 每行数据
         */
        handleMeetingClick(row) {
            this.$router.push({
                path: '/meetingDetail',
                query: { id: row.meetingId }
            });
        },
        /**
         * 判断会议是否已结束
         * @param {Object} row 每行数据
         * @return {Boolean} 会议是否已结束
         */
        isMinutesFinished(row) {
            return (
                row.minutesStatus === '任务未关闭' ||
                row.minutesStatus === '任务已关闭' ||
                row.minutesStatus === '审核中'
            );
        }
    }
};
</script>

<style lang="scss" scoped>
.meeting-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    padding: 0px !important;
}
// 调整table表头颜色
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
</style>
