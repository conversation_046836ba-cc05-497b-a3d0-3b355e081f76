<template>
    <div>
        <div class="box-main">
            <el-tabs v-model="activeName">
                <el-tab-pane label="我的会议" name="MeetingTaskSearch" lazy>
                    <MyMeetingsQueryList
                        ref="myMeetingsQueryList"
                        class="query-list"
                    ></MyMeetingsQueryList>
                </el-tab-pane>
                <el-tab-pane label="会议室查询" name="MeetingRoomQuery" lazy>
                    <BookDraggableRoom
                        ref="bookDraggableRoom"
                    ></BookDraggableRoom>
                </el-tab-pane>
                <el-tab-pane
                    label="参会人员查询"
                    name="JudgeAttendanceSearch"
                    lazy
                >
                    <JudgeAttendanceQuery
                        ref="judgeAttendanceQuery"
                    ></JudgeAttendanceQuery>
                </el-tab-pane>
                <el-tab-pane label="会议列表" name="MeetingSearch" lazy>
                    <MeetingSearch ref="meetingSearch"></MeetingSearch>
                </el-tab-pane>

                <el-tab-pane label="会议任务" name="MeetingTaskTracker" lazy>
                    <MeetingTaskTrace ref="meetingTaskTrace"></MeetingTaskTrace>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import JudgeAttendanceQuery from './components/judgeAttendanceQuery';
import MeetingSearch from './components/meetingSearch';
import MeetingTaskTrace from './components/meetingTaskTrace';
import MyMeetingsQueryList from './components/myMeetings/MyMeetingsQueryList.vue';
import BookDraggableRoom from './components/BookDraggableRoom';
import { getExternalStaffPeople } from 'feature/views/meetingManagement/commonFunction';

export default {
    name: 'MeetingManagement',
    components: {
        JudgeAttendanceQuery,
        MeetingSearch,
        MeetingTaskTrace,
        MyMeetingsQueryList,
        BookDraggableRoom
    },
    data() {
        return {
            activeName: 'MeetingTaskTracker'
        };
    },
    watch: {
        activeName(newVal) {
            // 当页签切换时，触发对应组件的查询
            this.$nextTick(() => {
                this.triggerComponentQuery(newVal);
            });
        }
    },
    created() {
        // 缓存页面
        this.$store.dispatch('tagsView/addView', this.$route);
        getExternalStaffPeople(this);
    },
    methods: {
        /**
         * 根据激活的页签名称触发对应组件的查询
         * @param {String} tabName 页签名称
         */
        triggerComponentQuery(tabName) {
            switch (tabName) {
                case 'MeetingTaskSearch':
                    // 我的会议
                    if (
                        this.$refs.myMeetingsQueryList &&
                        this.$refs.myMeetingsQueryList.getMeetingList
                    ) {
                        this.$refs.myMeetingsQueryList.getMeetingList();
                    }
                    break;
                case 'MeetingRoomQuery':
                    // 会议室查询 - 如果有查询方法的话
                    if (
                        this.$refs.bookDraggableRoom &&
                        this.$refs.bookDraggableRoom.refreshData
                    ) {
                        this.$refs.bookDraggableRoom.refreshData();
                    }
                    break;
                case 'JudgeAttendanceSearch':
                    // 参会人员查询
                    if (
                        this.$refs.judgeAttendanceQuery &&
                        this.$refs.judgeAttendanceQuery.getList
                    ) {
                        this.$refs.judgeAttendanceQuery.getList();
                    }
                    break;
                case 'MeetingSearch':
                    // 会议列表
                    if (
                        this.$refs.meetingSearch &&
                        this.$refs.meetingSearch.getList
                    ) {
                        this.$refs.meetingSearch.getList();
                    }
                    break;
                case 'MeetingTaskTracker':
                    // 会议任务
                    if (
                        this.$refs.meetingTaskTrace &&
                        this.$refs.meetingTaskTrace.getList
                    ) {
                        this.$refs.meetingTaskTrace.getList();
                    }
                    break;
                default:
                    break;
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.box-main {
    width: 100%;
    padding: 10px 20px 0 20px;
    background-color: #ffffff;
    height: 100vh;
    overflow: auto;
}
::v-deep #pane-second {
    border: 1px solid #8c8c8c !important;
}
</style>
